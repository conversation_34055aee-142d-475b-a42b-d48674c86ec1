<script setup lang="ts">
import config from '../../config/config' // 导入 config.js 配置、
import historyIcon from '@/assets/images/ai/history.png'
// import rotbotIcon from '@/assets/images/ai/robot.webp'
import wx from 'weixin-webview-jssdk'
import { showToast, showSuccessToast, showFailToast } from 'vant'

import { computed, ref, h, onMounted, nextTick } from 'vue'
import { Badge, Button, Space, theme, Typography } from 'ant-design-vue'
import {
  Bubble,
  Conversations,
  Prompts,
  Sender,
  Welcome,
  useXAgent,
  useXChat,
  Attachments,
  type BubbleListProps,
  type ConversationsProps,
} from 'ant-design-x-vue'
import {
  CopyOutlined,
  EllipsisOutlined,
  PaperClipOutlined,
  PlusOutlined,
  ShareAltOutlined,
  SmileOutlined,
  SyncOutlined,
  UserOutlined,
  FireOutlined,
  ReadOutlined,
  InfoCircleOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import markdownIt from 'markdown-it'
import hljs from 'highlight.js'
import * as echarts from 'echarts'
import { api } from '@/api/ai'
import { useRoute } from 'vue-router'
import router from '@/router'
import { alertProps } from 'element-plus'
import { getDictApiList } from '@/api/reportApi'

// ==================== markdown ============================
const md = markdownIt({
  html: true,
  breaks: true,
  linkify: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return ''
  },
})

const rptUrlReg = /https:\/\/www.livenhorizon.com\/(.+)/i
// 自定义URL渲染处理器，处理url连接地址改写
function rewriteUrl(url) {
  console.log(url, 'url')
  const match = url.match(rptUrlReg)
  let askKey = match ? match[1] : ''
  console.log(askKey, 'askKey')
  if (askKey !== '') {
    dictApi.value.forEach((item) => {
      if (item.apiKey.includes(askKey) || item.procedureName.includes(askKey) || item.apiName.includes(askKey)) {
        askKey = item.apiKey
        console.log(askKey, 'askKey==replace')
        return
      }
    })
  }
  const userId = sessionStorage.getItem('userId') || ''
  const token = localStorage.getItem('setToken') || ''
  const u = new URL('https://www.livenhorizon.com/nexora-front/') // 处理相对路径
  u.searchParams.set('t', token)
  u.searchParams.set('u', userId)
  u.searchParams.set('k', askKey)
  return u.toString()
}
// 覆盖链接渲染规则
const defaultLinkOpen =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  const token = tokens[idx]
  const hrefIndex = token.attrIndex('href')
  if (hrefIndex >= 0) {
    const originalUrl = token.attrs[hrefIndex][1]
    if (rptUrlReg.test(originalUrl)) {
      token.attrs[hrefIndex][1] = rewriteUrl(originalUrl)
    }
  }
  return defaultLinkOpen(tokens, idx, options, env, self)
}

// 自定义渲染器：处理 echarts 代码块
md.renderer.rules.fence = (tokens, idx, options, env, self) => {
  const token = tokens[idx]
  const code = token.content.trim()
  const lang = token.info.trim()

  if (lang === 'echarts') {
    try {
      const jsonData = JSON.parse(code)
      const chartId = `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      nextTick(() => {
        const chartElement = document.getElementById(chartId)
        if (chartElement) {
          const chart = echarts.init(chartElement)
          chart.setOption(jsonData)
        }
      })

      return `<div id="${chartId}" style="width: 100%; height: 300px;"></div>`
    } catch (error) {
      // console.error('JSON 解析失败:', error);
      return `<pre><code>${code}</code></pre>`
    }
  }

  return `<pre><code class="language-${lang}">${code}</code></pre>`
}

// 自定义渲染器：处理 表格样式
md.renderer.rules.table_open = (tokens, idx, options, env, self) => {
  return '<div class="table-responsive"><table>'
}
md.renderer.rules.table_close = (tokens, idx, options, env, self) => {
  return '</table></div>'
}
// ==================== markdown ============================

// ==================== State ====================
const headerOpen = ref(false)
const submitContent = ref('')
const conversationsItems = ref([])
const activeKey = ref('')
const cozeConversationId = ref('')
const cozeBotId = ref('')
const cozeChatId = ref('')
const localChatId = ref('')
const action = ref('CHAT')
const attachedFiles = ref([])
const attachedShowFiles = ref([])
const answerFinished = ref(true)
const conversationList = ref([])
const suggestionList = ref([])
const ossFileUrl = ref('')
const customPromptId = ref('')
const cardIcon = ref(InfoCircleOutlined)
const cardMsg = ref('')
const uploadUrl = ref('')
const uploadHeader = ref({})
const recordLocalId = ref('')
const speechConfig = ref<SpeechConfig>({
  recording: false,
  onRecordingChange: (nextRecording) => {
    // 输入框语音按钮点击回调
    speechConfig.value.recording = nextRecording
    if (nextRecording) {
      wx.startRecord()
    } else {
      wx.stopRecord({
        success: function (res) {
          console.log(JSON.stringify(res))
          recordLocalId.value = res.localId
          wx.translateVoice({
            localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得
            isShowProgressTips: 1, // 默认为1，显示进度提示
            success: function (res) {
              console.log('识别结果：', res)
              submitContent.value += res.translateResult
            },
            fail: function (res) {
              console.log('识别失败：', JSON.stringify(res))
            },
          })
        },
        fail: function (res) {
          console.log('停止录音失败：', JSON.stringify(res))
        },
      })
    }
  },
})
const menuConfig = ref<ConversationsProps['menu']>((conversation) => ({
    items: [
      // {
      //   label: '重命名',
      //   key: 'rename',
      //   icon: () => h(EditOutlined),
      // },
      {
        label: '删除',
        key: 'delete',
        icon: () => h(DeleteOutlined),
        danger: true,
      },
    ],
    onClick: (menuInfo) => {
      menuInfo.domEvent.stopPropagation();
      console.log(`Click ${conversation.key} - ${menuInfo.key}`);
      if (menuInfo.key === 'rename') {
        onRenameConversation(conversation.key)
      } else if (menuInfo.key === 'delete') {
        onDeleteConversation(conversation.key)
      }
    },
  }))
const dictApi = ref([])
// ==================== State ====================

// ==================== Style ====================
const { token } = theme.useToken()
const styles = computed(() => {
  return {
    layout: {
      width: '100%',
      // minWidth: '1000px',
      // height: '722px',
      boxSize: 'border-box',
      borderRadius: `${token.value.borderRadius}px`,
      display: 'flex',
      background: `${token.value.colorBgContainer}`,
      fontFamily: `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
      position: 'relative',
    },
    menu: {
      background: `${token.value.colorBgLayout}80`,
      width: '280px',
      height: '100%',
      display: 'flex',
      flexDirection: 'column' as const,
    },
    conversations: {
      padding: '0 12px',
      flex: 1,
      overflowY: 'auto' as const,
    },
    chat: {
      height: '100vh',
      width: '100%',
      maxWidth: '700px',
      margin: '0 auto',
      boxSizing: 'border-box' as const,
      display: 'flex',
      flexDirection: 'column' as const,
      padding: `${token.value.paddingLG}px`,
      gap: '16px',
    },
    messages: {
      flex: 1,
      margin: '0 0 150px 0',
    },
    placeholder: {
      paddingTop: '32px',
    },
    sender: {
      // position: 'fixed',
      // bottom: '30px',
      // left: '10px',
      // right: '10px',
      width: 'auto' /* 用 left + right 控制宽度 */,
      boxSize: 'border-box',
      boxShadow: `${token.value.boxShadow}`,
      zIndex: 100,
      background: '#fff',
    },
    logo: {
      display: 'flex',
      height: '72px',
      alignItems: 'center' as const,
      justifyContent: 'flex-start' as const,
      padding: '0 24px',
      boxSizing: 'border-box' as const,
    },
    'logo-img': {
      width: '24px',
      height: '24px',
      display: 'inline-block',
    },
    'logo-span': {
      display: 'inline-block',
      margin: '0 8px',
      fontWeight: 'bold' as const,
      color: token.value.colorText,
      fontSize: '16px',
    },
    addBtn: {
      background: '#1677ff0f',
      border: '1px solid #1677ff34',
      width: 'calc(100% - 24px)',
      margin: '0 12px 24px 12px',
    },
    welcome: {
      backgroundImage: 'linear-gradient(97deg, rgb(235, 242, 255) 0%, rgb(242, 234, 255) 100%)',
      borderRadius: '10px',
      padding: '10px',
    },
    // aiIcon: `${config.aiBaseURL}` + rotbotIcon.substring(1),
    aiIcon: `${config.aiBaseURL}/nexora-img/nexora-front/ai/robot.webp`,
  }
})

const roles: BubbleListProps['roles'] = {
  ai: {
    placement: 'start',
    avatar: { icon: h('img', { src: styles.value.aiIcon }), style: { background: '#fde3cf' } },
    typing: false,
    messageRender: (content) =>
      h(Typography, null, {
        default: () => h('div', { innerHTML: md.render(content) }),
      }),
    // footer: h(Space, { size: token.value.paddingXXS }, [
    //   h(Button, { type: 'text', size: 'small', icon: h(SyncOutlined) }),
    //   h(Button, { type: 'text', size: 'small', icon: h(CopyOutlined) }),
    // ]),
    // header: h(Space, {}, [
    //   h(
    //     Button,
    //     {
    //       type: 'default',
    //       style: { backgroundColor: 'rgba(0, 0, 0, 0.06)', color: 'rgba(0,178,60,1)' },
    //       icon: h(cardIcon.value, { style: { color: '#000' } }),
    //     },
    //     cardMsg.value,
    //   ),
    // ]),
  },
  user: {
    placement: 'end',
    variant: 'shadow',
    avatar: { icon: h(UserOutlined), style: { background: '#87d068' } },
  },
  suggestion: {
    placement: 'start',
    avatar: { icon: UserOutlined, style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (content) =>
      h(Prompts, {
        vertical: true,
        items: (content as any as Suggestion[]).map((suggestion) => ({
          key: suggestion.actionCode + '/' + suggestion.localChatId + '/' + suggestion.cozeChatId,
          icon: h(SmileOutlined, { style: { color: '#FAAD14' } }),
          description: suggestion.content,
        })),
        onItemClick: ({ data }) => {
          action.value = data.key.split('/')[0]
          localChatId.value = data.key.split('/')[1]
          cozeChatId.value = data.key.split('/')[2]
          const msg = data.description

          // 触发一次 chat 接口调用
          onRequest({
            type: 'user',
            content: msg as string,
          })
        },
      }),
  },
}
// ==================== type    ====================

type RespBody = {
  code: number
  message: string
  responseTime?: number
  traceId?: string
  serviceId?: string
  result?: any
}

type Suggestion = {
  content: string
  actionCode: string
  cozeChatId: string
  localChatId: string
}

type AgentUserMessage = {
  type: 'user'
  content: string
}

type AgentAIMessage = {
  type: 'ai'
  content?: string
  list?: [
    {
      type: 'ai'
      content: string
    },
    {
      type: 'suggestion'
      content: Suggestion[]
    },
  ]
}

type AgentMessage = AgentUserMessage | AgentAIMessage

type BubbleMessage = {
  role: string
  content: string
}

type SpeechConfig = {
  recording: boolean
  onRecordingChange: (nextRecording: boolean) => void
}

// ==================== Runtime ====================

const abortControllerRef = ref(null)
const aiMsgContent = ref('')

const [agent] = useXAgent<AgentMessage>({
  request: async (info, callbacks) => {
    const { messages, message } = info
    const { onUpdate, onSuccess } = callbacks

    aiMsgContent.value = ''
    answerFinished.value = false

    // 更新会话标题
    const title = message.content.substring(0, 20)
    conversationsItems.value.map((item) => {
      if (item.key == activeKey.value && item.label === '新会话' && title !== ' ') {
        item.label = title
      }
    })

    if (abortControllerRef.value) {
      abortControllerRef.value.abort()
    }

    const abortController = new AbortController()
    abortControllerRef.value = abortController

    // ${config.aiBaseURL}

    let url = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/stream`
    let path = ''
    if (promptsItemskey.value === 'industryData') {
      path = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/upc/stream`
    } else if (promptsItemskey.value === 'goodsDetails') {
      path = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/industry/stream`
    } else {
      path = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/stream`
    }
    try {
      const response = await fetch(
        path, // `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/stream`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'angela-a-token': sessionStorage.getItem('angela-a-token'),
            'vdf-source': 'vdf-front',
          },
          body: JSON.stringify({
            prompt: message.content,
            localConversationId: activeKey.value,
            localChatId: localChatId.value,
            cozeBotId: cozeBotId.value,
            cozeConversationId: cozeConversationId.value,
            cozeChatId: cozeChatId.value,
            actionCode: action.value,
            // ossFileUrl: ossFileUrl.value,
            ossFiles: attachedFiles.value,
            customPromptId: customPromptId.value,
          }),
          signal: abortControllerRef.value.signal,
        },
      )
      // promptsItemskey.value = ''
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) throw new Error('No reader available')

      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunkStr = decoder.decode(value)

        const lines = chunkStr.split('\n').filter(Boolean)

        for (const line of lines) {
          const sseString = line.startsWith('data:') ? line.slice('data:'.length) : line
          if (sseString.length < 1) continue
          let sseEvent: RespBody
          try {
            sseEvent = JSON.parse(sseString)
          } catch (err) {
            console.error('Error parsing SSE line:', err, line)
            continue
          }

          const apiRetCode = sseEvent.code
          if (apiRetCode != 0) {
            // 处理鉴权失败的情况
            aiMsgContent.value = sseEvent.message
            onUpdate({
              type: 'ai',
              content: aiMsgContent.value,
            })

            // 关闭读流
            await reader.cancel()
            return
          } else {
            if (sseEvent.result?.cardMsg === '思考结束') {
              // 清空之前发的正在思考
              aiMsgContent.value = ''
            }
            // 更新当前对话的coze会话id，botId，消息内容
            aiMsgContent.value += sseEvent.result?.message
            cozeConversationId.value = sseEvent.result?.conversationId
            cozeBotId.value = sseEvent.result.botId

            // suggestion 列表
            if (sseEvent.result.suggestions && sseEvent.result.suggestions.length > 0) {
              suggestionList.value = sseEvent.result.suggestions
            } else {
              suggestionList.value = []
            }

            onUpdate({ type: 'ai', content: aiMsgContent.value })
          }
        }
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('用户停止对话')
        return
      }
      console.error('Error sending message:', error)

      aiMsgContent.value = '系统繁忙，请稍后再试~'
      onUpdate({ type: 'ai', content: aiMsgContent.value })
    } finally {
      abortControllerRef.value = null
      answerFinished.value = true
      // action.value = 'CHAT'

      if (suggestionList.value.length > 0) {
        onSuccess({
          type: 'ai',
          list: [
            {
              type: 'ai',
              content: aiMsgContent.value,
            },
            {
              type: 'suggestion',
              content: suggestionList.value,
            },
          ],
        })
      } else {
        if (aiMsgContent.value.includes('<img src=')) {
          aiMsgContent.value = aiMsgContent.value.replace(
            /<img src=/,
            '<img class="zoomable-img" onclick="this.classList.toggle(\'zoomed\')" src=',
          )
        }
        onSuccess({ type: 'ai', content: aiMsgContent.value })
      }
    }
  },
})

const { onRequest, parsedMessages, setMessages } = useXChat<AgentMessage, BubbleMessage>({
  agent: agent.value,
  requestPlaceholder: {
    type: 'ai',
    content: '',
  },
  parser: (agentMessages) => {
    // const list = agentMessages.content ? [agentMessages] : (agentMessages as AgentAIMessage).list
    const list =
      typeof agentMessages.content !== 'undefined'
        ? [agentMessages]
        : (agentMessages as AgentAIMessage).list

    return (list || []).map((msg) => ({
      role: msg.type,
      content: msg.content,
    }))
  },
})

// ==================== events ===================
const onAddConversation = () => {
  api
    .newConversation()
    .then((res: any) => {
      const newConversation = conversationsItems.value.filter((item) => item.key == res.result.id)
      // 清理附件
      attachedFiles.value = []
      attachedShowFiles.value = []
      headerOpen.value = false
      action.value = 'CHAT'
      if (newConversation.length > 0) {
        activeKey.value = res.result.id
        setMessages([])
        // TODO 弹出消息提示已是最新会话
      } else {
        conversationsItems.value = [
          {
            key: res.result.id,
            label: '新会话',
          },
          ...conversationsItems.value,
        ]
        activeKey.value = res.result.id
        cozeConversationId.value = ''
        cozeBotId.value = ''
        cozeChatId.value = ''
        localChatId.value = ''
        setMessages([])
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const onConversationClick = (key) => {
  activeKey.value = key
  if (cozeConversationId.value != key) {
    cozeConversationId.value = ''
  }
  drawerVisible.value = false
  initMessages()
}

const promptsItemskey = ref('')

const onPromptsItemClick = (info) => {
  console.log('Prompt clicked:', info.data.key)
  console.log('info', info.data.description)

  if (info.data.key * 1 === 1) {
    drawerVisible.value = true
  } else if (info.data.key * 1 === 2) {
    promptsItemskey.value = ''
    onAddConversation()
  } else if (info.data.key === 'industryData' || info.data.key === 'goodsDetails') {
    promptsItemskey.value = info.data.key
    onRequest({
      type: 'user',
      content: info.data.description,
    })
  } else {
    promptsItemskey.value = ''
    onRequest({
      type: 'user',
      content: info.data.description,
    })
  }
}

const handleFileChange = (info) => {
  if (info.file.status !== 'uploading') {
    console.log('handleFileChange', info)
  }
  if (info.file.status === 'done' || info.file.status === 'removed') {
    // 提取所有 result 并合并成一个新数组
    const allResults = info.fileList.flatMap((item) => item.response.result)
    // 直接替换 attachedFiles
    attachedFiles.value = allResults
    // 展示用
    attachedShowFiles.value = info.fileList
  } else if (info.file.status === 'error') {
    console.log(`${info.file.name} file upload failed.`)
  }
  // 行业数据查询和商品标库查询上传了附件可以发送空内容
  if (
    attachedShowFiles.value.length > 0 &&
    (promptsItemskey.value === 'industryData' || promptsItemskey.value === 'goodsDetails')
  ) {
    submitContent.value = ' '
  }
  console.log('attachedFiles', attachedFiles.value)
  console.log('attachedShowFiles', attachedShowFiles.value)
}

const handleFile = () => {
  headerOpen.value = !headerOpen.value
  console.log('headerOpen.value', headerOpen.value)
  console.log('attachedFiles', attachedFiles.value)
  console.log('attachedShowFiles', attachedShowFiles.value)
}

const onSubmit = (nextContent) => {
  if (!nextContent) return
  // 向bubblelist推入新消息，发起新请求
  onRequest({
    type: 'user',
    content: nextContent,
  })
  submitContent.value = ''
  if (
    attachedShowFiles.value.length > 0 &&
    (promptsItemskey.value === 'industryData' || promptsItemskey.value === 'goodsDetails')
  ) {
    submitContent.value = ' '
  }

  if (senderRef.value) {
    senderRef.value.blur()
  }
}

// 点击停止按钮
const onCancel = () => {
  // 前端停止接收
  if (abortControllerRef.value) {
    abortControllerRef.value.abort()
    abortControllerRef.value = null
  }
}

// 重命名会话标题
const onRenameConversation = (key) => {
  // TODO 重命名会话标题
  showFailToast('重命名功能未开放')
}

// 删除会话
const onDeleteConversation = (key) => {
  api
    .deleteConversation(key)
    .then((res: any) => {
      console.log('deleteConversation', res)
      if (res.code === 0) {
        showSuccessToast('删除成功')
        // 删除成功，从会话列表中移除
        conversationsItems.value = conversationsItems.value.filter((item) => item.key != key)
        // 切换到第一个会话
        activeKey.value = conversationsItems.value[0].key
        initMessages()
      } else {
        showFailToast('删除失败')
      }
    })
    .catch((error) => {
      console.log('deleteConversation', error)
      showFailToast('删除失败')
    })
}

// ==================== Nodes ====================

let bubbleItems = computed(() => {
  return parsedMessages.value.map(({ id, message, status }) => ({
    key: id,
    // role: message.type,
    loading: message.content.length === 0,
    // loading: true,
    ...message,
  }))
})

const renderTitle = (icon, title) => {
  return h(Space, { align: 'start' }, [icon, h('span', {}, title)])
}

const placeholderPromptsItems = [
  {
    key: '1',
    label: renderTitle(h(FireOutlined, { style: { color: '#FF4D4F' } }), '热门话题'),
    // description: '您可以这样问我：',
    children: [
      {
        key: '1-1',
        description: `系统中有哪些标准报表？`,
      },
      {
        key: '1-3',
        description: `周末和工作日的销售模式有何差异？`,
      },
      {
        key: '1-2',
        description: `统计中心广场店上个月的销售额和销售量。销售量计算时乘以10。`,
      },
    ],
  },
  {
    key: '2',
    label: renderTitle(h(ReadOutlined, { style: { color: '#FF4D4F' } }), 'AI应用'),
    // description: '还可以查看商品数据：',
    children: [
      {
        key: 'industryData',
        // icon: h(HeartOutlined),
        // description: `查看商品行业数据 例：查看行业数据+条码`,
        description: `行业数据查询`,
      },
      {
        key: 'goodsDetails',
        // icon: h(SmileOutlined),
        description: `商品标库查询`,
      },
      // {
      //   key: '2-3',
      //   icon: h(CommentOutlined),
      //   description: `Express the feeling`,
      // },
    ],
  },
]

const initConversations = async () => {
  await api
    .conversationList({})
    .then((res: any) => {
      res.result.map((e) => {
        conversationsItems.value.push({
          key: e.id,
          label: e.title ? e.title : '新会话',
        })
      })
      if (res.result.length > 0) {
        conversationList.value = res.result
        activeKey.value = res.result[0].id
        const msgs = res.result[0].messagesInfo
        if (msgs.length > 0) {
          cozeConversationId.value = msgs[msgs.length - 1].conversationId || ''
          cozeBotId.value = msgs[msgs.length - 1].botId || ''
        }
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const initMessages = () => {
  if (conversationList.value.length > 0) {
    console.log('localConversationId:', activeKey.value)
    // 获取对话中的消息列表
    api
      .conversationDetail(activeKey.value)
      .then((res: any) => {
        const messageInfo = res.result.messagesInfo

        // 取会话中，最后调用的conversationId, botId
        if (messageInfo.length > 0) {
          let msgs = []
          // 取会话消息列表
          messageInfo.forEach((e, index) => {
            try {
              const content = JSON.parse(e.content)
              content.forEach((c) => {
                msgs.push({
                  id: res.result.id + '_' + index,
                  status: e.role == 'user' ? 'local' : 'success',
                  message: {
                    type: e.role == 'user' ? 'user' : 'ai',
                    content: c.type == 'text' ? c.text : c.fileName,
                  },
                })
              })
            } catch {
              if (e.role !== 'user') {
                e.content = e.content.replace(
                  /<img src=/,
                  '<img class="zoomable-img" onclick="this.classList.toggle(\'zoomed\')" src=',
                )
              }
              msgs.push({
                id: res.result.id + '_' + index,
                status: e.role == 'user' ? 'local' : 'success',
                message: {
                  type: e.role == 'user' ? 'user' : 'ai',
                  content: e.content,
                },
              })
            }
          })
          //   roles.ai.typing = false
          setMessages(msgs)

          const aiMsgs = messageInfo.filter((msg) => msg.role == 'assistant')
          if (aiMsgs.length > 0) {
            cozeConversationId.value = aiMsgs[aiMsgs.length - 1].conversationId
            cozeBotId.value = aiMsgs[aiMsgs.length - 1].botId
          }
        }
      })
      .catch((error) => {
        console.log(error)
      })
  } else {
    api.newConversation().then((res: any) => {
      activeKey.value = res.result.id
      setMessages([])
    }).catch((error) => {
      console.log(error)
    })
  }
  setMessages([])
}

const senderRef = ref<InstanceType<typeof Sender> | null>(null)

// const senderTopPromptsItems = [
//   {
//     key: 'industryData',
//     description: '行业数据',
//     //  icon: h('img', {
//     //  src: historyIcon,
//     //   alt: '',
//     //   style: { width: '24px', height: '24px' },
//     //  }),
//   },
//   {
//     key: 'goodsDetails',
//     description: '商品详情',
//     // icon: h(ReadOutlined, { color: '#1890FF' }),
//   },
// ]

const senderPromptsItems = [
  {
    key: '1',
    description: '历史话题',
    //  icon: h('img', {
    //  src: historyIcon,
    //   alt: '',
    //   style: { width: '24px', height: '24px' },
    //  }),
  },
  {
    key: '2',
    description: '开启新对话',
    // icon: h(ReadOutlined, { color: '#1890FF' }),
  },
]

const reportInsight = () => {
  if (typeof route.query.a !== 'undefined' && route.query.a == 'report') {
    const reportInfo = JSON.parse(sessionStorage.getItem('reportInfo') || '{}')
    console.log('reportInfo:', reportInfo)

    action.value = 'REPORT_INSIGHT'
    // ossFileUrl.value = reportInfo.url || ''
    if (reportInfo.url) {
      attachedFiles.value.push({
        type: 'file',
        ossFileUrl: reportInfo.url,
      })
    }
    // 测试用的标准报表文件
    // ossFileUrl.value = 'nexora/rptana/guest/20250509201031.csv'

    // 发起会话
    onRequest({
      type: 'user',
      content: '解读报表：' + reportInfo.name || '',
    })
    submitContent.value = ''
  }
}

const loadDictApiList = () => {
  getDictApiList({}).then((res) => {
    dictApi.value = res
  }).catch((error) => {
    console.log(error)
  })
}

// 未登录跳转小程序
const loginStatus = ref(true)
const handleClick = () => {
  const exitValue = sessionStorage.getItem('exit')
  if (exitValue === 'exit') {
    wx.miniProgram.redirectTo({ url: '../login/login' })
  }
  if (!loginStatus.value) {
    wx.miniProgram.redirectTo({ url: '../autoLogin/autoLogin' })
  }
  // if (exitValue !== null && !loginStatus.value) {
  //   wx.miniProgram.redirectTo({ url: '../autoLogin/autoLogin' })
  // }
  // if (exitValue === null && !loginStatus.value) {
  //   wx.miniProgram.redirectTo({ url: '../login/login' })
  // }
}
// 未登录跳转小程序

const personalCenter = ref(false)
const nexoraAuthLogin = () => {
  api
    .nexoraAuthLogin()
    .then(async (res: any) => {
      if (res.code * 1 === 0) {
        if (res.result.userType * 1 === 0) {
          personalCenter.value = true
        }
        sessionStorage.setItem('userInfo', JSON.stringify(res.result))
        // 获取jssdk的WxJsapiSignature
        wxConfig()
        // 设置上传配置
        setUploadConf()
        // 初始化会话
        await initConversations()
        // 初始化会话内的消息
        await initMessages()
        // 报表解读
        await reportInsight()
        // 获取报表askKey
        await loadDictApiList()
      } else {
        showToast({
          type: 'fail',
          message: 'res.message',
          duration: 3000,
        })
        loginStatus.value = false
        wx.miniProgram.redirectTo({ url: '../login/login' })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res: any) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['startRecord', 'stopRecord', 'translateVoice'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

const setUploadConf = () => {
  uploadUrl.value = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/uploadFile`
  uploadHeader.value = {
    'angela-a-token': sessionStorage.getItem('angela-a-token'),
    'vdf-source': 'vdf-front',
  }
}

const onMyClick = () => {
  wx.miniProgram.redirectTo({ url: '../my/my' })
}

const route = useRoute()

onMounted(async () => {
  document.title = '商运星智能助手'
  console.log(route.query, 'AI route.query')
  if (JSON.stringify(route.query) !== '{}') {
    if (typeof route.query.t !== 'undefined' && typeof route.query.u !== 'undefined') {
      const routeToken = Array.isArray(route.query.t) ? route.query.t[0] : route.query.t
      localStorage.setItem('setToken', routeToken)

      const userId = Array.isArray(route.query.u) ? route.query.u[0] : route.query.u
      sessionStorage.setItem('userId', userId)
      sessionStorage.setItem('token', routeToken)
      loginStatus.value = true
      // 登录
      await nexoraAuthLogin()
    }
    if (typeof route.query.e !== 'undefined') {
      const exit = Array.isArray(route.query.e) ? route.query.e[0] : route.query.e
      sessionStorage.setItem('exit', exit)
    }
  }

  wx.error((res) => {
    // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
    console.log(JSON.stringify(res))
  })
})

// 抽屉相关状态
const drawerVisible = ref(false)
</script>

<template>
  <div :style="styles.layout" @click="handleClick">
    <!-- <div class="list-switch">
      <van-icon name="list-switch" @click="drawerVisible = !drawerVisible" size="20px" />
    </div> -->
    <!-- <div class="my" @click="onMyClick" v-if="personalCenter">个人中心</div> -->
    <!-- <Drawer
      :visible="drawerVisible"
      placement="left"
      :width="300"
      @close="drawerVisible = false"
      :closable="false"
    >
      <div class="drawer-content"> -->
    <van-popup
      v-model:show="drawerVisible"
      :style="{ paddingTop: '50px', height: '100%' }"
      position="left"
    >
      <div :style="styles.menu">
        <Button @click="onAddConversation" type="link" :style="styles.addBtn">
          <PlusOutlined />
          新会话
        </Button>
        <!-- 🌟 会话管理 -->
        <Conversations
          :items="conversationsItems"
          :style="styles.conversations"
          :activeKey="activeKey"
          @activeChange="onConversationClick"
          :menu="menuConfig"
        />
      </div>
    </van-popup>
    <!-- </div>
    </Drawer> -->

    <div :style="styles.chat">
      <Space
        v-if="!bubbleItems || bubbleItems.length == 0"
        direction="vertical"
        :size="16"
        :style="styles.placeholder"
      >
        <!-- description="我可以为您解答零售相关问题、帮您解读零售数据指标含义和潜在的价值、为您提升销售出谋划策、也根据您的要求自由生成自定义报表" -->
        <Welcome
          variant="borderless"
          title="Hello, 我是灵犀助手"
          description="我可以帮您诊断问题，深度解析数据指标，提供多维度策略支持与定制化报表生成，驱动业绩持续增长~"
          :style="styles.welcome"
          :icon="styles.aiIcon"
        >
          <template #actions>
            {() => (
            <Space>
              <Button :icon="h(ShareAltOutlined)" />
              <Button :icon="h(EllipsisOutlined)" />
            </Space>
            )}
          </template>
        </Welcome>
        <Prompts
          title=""
          :items="placeholderPromptsItems"
          wrap
          :styles="{
            item: {
              flex: 'none',
              width: 'calc(50% - 6px)',
              backgroundImage: `linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)`,
              border: 0,
            },
            subItem: {
              background: 'rgba(255,255,255,0.45)',
              border: '1px solid #FFF',
            },
          }"
          @itemClick="onPromptsItemClick"
        />
      </Space>

      <!-- 🌟 消息列表 -->
      <Bubble.List
        :autoScroll="true"
        v-if="bubbleItems && bubbleItems.length > 0"
        :items="bubbleItems"
        :roles="roles"
        :style="styles.messages"
      />

      <!-- 🌟 提示词
            <Prompts :style="{ color: token.colorText }" :items="senderPromptsItems" @itemClick="onPromptsItemClick" />
            -->

      <!-- 🌟 输入框 -->

      <div class="sender-box">
        <!-- <Prompts
          :items="senderTopPromptsItems"
          @item-click="onPromptsItemClick"
          :style="{
            marginBottom: '5px',
            border: 'none',
            // display: 'flex',
            // justifyContent: 'flex-end',
          }"
        />-->
        <Sender
          ref="senderRef"
          submit-type="shiftEnter"
          :loading="!answerFinished"
          v-model:value="submitContent"
          @submit="onSubmit"
          :style="styles.sender"
          :allowSpeech="speechConfig"
          @cancel="onCancel"
        >
          <template #prefix>
            <Badge :dot="attachedShowFiles.length > 0 && !headerOpen">
              <Button type="text" :icon="h(PaperClipOutlined)" @click="handleFile" />
            </Badge>
          </template>
          <template #header>
            <Sender.Header
              title="上传文件"
              :open="headerOpen"
              :styles="{ content: { padding: 0 } }"
              @open-change="(open) => (headerOpen = open)"
            >
              <Attachments
                accept=".jpg,.png,.jpeg,.xls,.xlsx,.csv"
                name="files"
                :before-upload="() => true"
                v-model:items="attachedShowFiles"
                :action="uploadUrl"
                :headers="uploadHeader"
                @change="handleFileChange"
              >
                <template #placeholder="type">
                  <Flex
                    v-if="type && type.type === 'inline'"
                    align="center"
                    justify="center"
                    vertical
                    gap="2"
                  >
                    <Typography.Text style="font-size: 30px; line-height: 1">
                      <CloudUploadOutlined />
                    </Typography.Text>
                    <Typography.Title
                      :level="5"
                      style="margin: 0; font-size: 14px; line-height: 1.5"
                    >
                      上传文件
                    </Typography.Title>
                    <!-- <Typography.Text type="secondary"> 点击上传 </Typography.Text> -->
                  </Flex>
                  <Typography.Text v-if="type && type.type === 'drop'">
                    Drop file here
                  </Typography.Text>
                </template>
              </Attachments>
            </Sender.Header>
          </template>
        </Sender>
        <Prompts
          :items="senderPromptsItems"
          @item-click="onPromptsItemClick"
          :style="{ marginTop: '10px', border: 'none' }"
        />

        <div class="tooltip">内容由AI生成仅供参考</div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
:where(.css-dev-only-do-not-override-1p3hq3p).ant-drawer .ant-drawer-body {
  padding: 50px 0 0 0;
  margin: 0 auto;
}

.ant-bubble-content {
  background-image: linear-gradient(97deg, rgb(235, 242, 255) 0%, rgb(242, 234, 255) 100%);
}

.list-switch {
  position: absolute;
  top: 30px;
  left: 30px;
}

.my {
  position: absolute;
  top: 25px;
  left: 90px;
}

.sender-box {
  position: fixed;
  bottom: 0;
  left: 10px;
  right: 10px;
  background-color: #fff;
  /* padding: 0 0 50px 0;*/
  padding-bottom: env(safe-area-inset-bottom);
  .tooltip {
    text-align: center;
    margin: 10px 0 0 0;
    font-size: 22px;
  }
}

/* 表格容器样式 */
.table-responsive {
  max-width: 100%;
  /* 不超过父容器宽度 */
  overflow-x: auto;
  /* 启用横向滚动 */
  -webkit-overflow-scrolling: touch;
  /* 移动端滚动优化 */
  border: 1px solid #dee2e6;
}

/* 表格主体样式 */
.table-responsive table {
  width: 100%;
  min-width: 600px;
  /* 保证内容较多时正常换行 */
  border-collapse: collapse;
  background: white;
}

/* 斑马纹效果 */
.table-responsive tr:nth-child(even) {
  background-color: #f8f9fa;
  /* 浅灰色背景 */
}

/* 单元格基础样式 */
.table-responsive td {
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
  /* 禁止自动换行 */
}

/* 表头样式 */
.table-responsive th {
  padding: 10px;
  background: #2987e5;
  /* 深蓝色背景 */
  color: white;
  font-weight: 600;
  text-align: left;
}

/* 悬停效果 */
.table-responsive tr:hover td {
  background-color: #e9ecef;
  /* 更深的悬停色 */
}

.table-responsive td,
.table-responsive th {
  border-left: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
}

/* 移除第一列左侧和最后一列右侧的边框 */
.table-responsive td:first-child,
.table-responsive th:first-child {
  border-left: none;
}

.table-responsive td:last-child,
.table-responsive th:last-child {
  border-right: none;
}

/* 会话标题弹出菜单不被遮挡 */
.ant-dropdown {
  z-index: 9999;
}

/* 放大图片 */
.zoomable-img {
  cursor: zoom-in;
  transition: transform 0.2s ease;
  max-width: 100%;
  display: block;
  margin: 0 auto;
}
.zoomable-img.zoomed {
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw !important;
  height: auto !important;
  max-height: 70vh !important;
  z-index: 1000;
  cursor: zoom-out;
  object-fit: contain;
}
// :where(.css-1s8prim).ant-bubble .ant-bubble-content-shadow {
//   background-image: linear-gradient(97deg, red 0%, red 100%);
// }
// :where(.css-1s8prim).ant-bubble .ant-bubble-content-filled {
//   background-image: linear-gradient(97deg, green 0%, green 100%);
// }
</style>
