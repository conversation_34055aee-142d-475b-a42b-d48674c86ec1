<script setup>
import { ref, onMounted, nextTick, computed } from 'vue'
import { Badge, Button, Space, theme, Typography } from 'ant-design-vue'
import {
  Bubble,
  Conversations,
  Prompts,
  Sender,
  Welcome,
  useXAgent,
  useXChat,
  Attachments,
} from 'ant-design-x-vue'
import {
  CopyOutlined,
  Ellip<PERSON>Outlined,
  PaperClipOutlined,
  PlusOutlined,
  ShareAltOutlined,
  SmileOutlined,
  SyncOutlined,
  UserOutlined,
  FireOutlined,
  ReadOutlined,
  InfoCircleOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import markdownIt from 'markdown-it'
import hljs from 'highlight.js'
import * as echarts from 'echarts'
import { api } from '@/api/ai'
import config from '@/config/config'

// Props
const props = defineProps({
  isPopup: {
    type: Boolean,
    default: false
  }
})

// 暴露方法给父组件
const initReportInsight = () => {
  reportInsight()
}

defineExpose({
  initReportInsight
})

// AI相关的状态和逻辑
const activeKey = ref('')
const localChatId = ref('')
const cozeChatId = ref('')
const cozeConversationId = ref('')
const cozeBotId = ref('')
const action = ref('')
const attachedFiles = ref([])
const customPromptId = ref('')
const submitContent = ref('')
const abortControllerRef = ref(null)

// 会话相关
const conversationsItems = ref([])
const bubbleItems = ref([])

// 样式配置
const { token } = theme.useToken()
const styles = computed(() => ({
  layout: {
    height: '100%',
    background: token.value.colorBgLayout,
    display: 'flex',
    flexDirection: 'column',
  },
  chat: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  placeholder: {
    flex: 1,
    padding: '24px',
    justifyContent: 'center',
  },
  welcome: {
    background: token.value.colorBgContainer,
    borderRadius: token.value.borderRadius,
    padding: '24px',
  },
  aiIcon: {
    color: '#1890ff',
    fontSize: '32px',
  },
}))

// Markdown渲染器
const md = markdownIt({
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return ''
  }
})

// 报表解读功能
const reportInsight = () => {
  const reportInfo = JSON.parse(sessionStorage.getItem('reportInfo') || '{}')
  const aiQuery = JSON.parse(sessionStorage.getItem('aiQuery') || '{}')

  if (aiQuery.a === 'report') {
    console.log('reportInfo:', reportInfo)

    action.value = 'REPORT_INSIGHT'
    if (reportInfo.url) {
      attachedFiles.value.push({
        type: 'file',
        ossFileUrl: reportInfo.url,
      })
    }

    // 发起会话
    onRequest({
      type: 'user',
      content: '解读报表：' + (reportInfo.name || ''),
    })
    submitContent.value = ''
  }
}

const loadDictApiList = () => {
  getDictApiList({}).then((res) => {
    dictApi.value = res
  }).catch((error) => {
    console.log(error)
  })
}

// 未登录跳转小程序
const loginStatus = ref(true)
const handleClick = () => {
  const exitValue = sessionStorage.getItem('exit')
  if (exitValue === 'exit') {
    wx.miniProgram.redirectTo({ url: '../login/login' })
  }
  if (!loginStatus.value) {
    wx.miniProgram.redirectTo({ url: '../autoLogin/autoLogin' })
  }
  // if (exitValue !== null && !loginStatus.value) {
  //   wx.miniProgram.redirectTo({ url: '../autoLogin/autoLogin' })
  // }
  // if (exitValue === null && !loginStatus.value) {
  //   wx.miniProgram.redirectTo({ url: '../login/login' })
  // }
}
// 未登录跳转小程序

const personalCenter = ref(false)
const nexoraAuthLogin = () => {
  api
    .nexoraAuthLogin()
    .then(async (res: any) => {
      if (res.code * 1 === 0) {
        if (res.result.userType * 1 === 0) {
          personalCenter.value = true
        }
        sessionStorage.setItem('userInfo', JSON.stringify(res.result))
        // 获取jssdk的WxJsapiSignature
        wxConfig()
        // 设置上传配置
        setUploadConf()
        // 初始化会话
        await initConversations()
        // 初始化会话内的消息
        await initMessages()
        // 报表解读
        await reportInsight()
        // 获取报表askKey
        await loadDictApiList()
      } else {
        showToast({
          type: 'fail',
          message: 'res.message',
          duration: 3000,
        })
        loginStatus.value = false
        wx.miniProgram.redirectTo({ url: '../login/login' })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res: any) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['startRecord', 'stopRecord', 'translateVoice'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

const setUploadConf = () => {
  uploadUrl.value = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/uploadFile`
  uploadHeader.value = {
    'angela-a-token': sessionStorage.getItem('angela-a-token'),
    'vdf-source': 'vdf-front',
  }
}

const onMyClick = () => {
  wx.miniProgram.redirectTo({ url: '../my/my' })
}

const route = useRoute()

onMounted(async () => {
  document.title = '商运星智能助手'
  console.log(route.query, 'AI route.query')
  if (JSON.stringify(route.query) !== '{}') {
    if (typeof route.query.t !== 'undefined' && typeof route.query.u !== 'undefined') {
      const routeToken = Array.isArray(route.query.t) ? route.query.t[0] : route.query.t
      localStorage.setItem('setToken', routeToken)

      const userId = Array.isArray(route.query.u) ? route.query.u[0] : route.query.u
      sessionStorage.setItem('userId', userId)
      sessionStorage.setItem('token', routeToken)
      loginStatus.value = true
      // 登录
      await nexoraAuthLogin()
    }
    if (typeof route.query.e !== 'undefined') {
      const exit = Array.isArray(route.query.e) ? route.query.e[0] : route.query.e
      sessionStorage.setItem('exit', exit)
    }
  }

  wx.error((res) => {
    // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
    console.log(JSON.stringify(res))
  })
})

// 抽屉相关状态
const drawerVisible = ref(false)
</script>

<template>
  <div :style="styles.layout" class="ai-component">
    <div :style="styles.chat">
      <Space
        v-if="!bubbleItems || bubbleItems.length == 0"
        direction="vertical"
        :size="16"
        :style="styles.placeholder"
      >
        <!-- description="我可以为您解答零售相关问题、帮您解读零售数据指标含义和潜在的价值、为您提升销售出谋划策、也根据您的要求自由生成自定义报表" -->
        <Welcome
          variant="borderless"
          title="Hello, 我是灵犀助手"
          description="我可以帮您诊断问题，深度解析数据指标，提供多维度策略支持与定制化报表生成，驱动业绩持续增长~"
          :style="styles.welcome"
          :icon="styles.aiIcon"
        >
          <template #actions>
            {() => (
            <Space>
              <Button :icon="h(ShareAltOutlined)" />
              <Button :icon="h(EllipsisOutlined)" />
            </Space>
            )}
          </template>
        </Welcome>
        <Prompts
          title=""
          :items="placeholderPromptsItems"
          wrap
          :styles="{
            item: {
              flex: 'none',
              width: 'calc(50% - 6px)',
              backgroundImage: `linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)`,
              border: 0,
            },
            subItem: {
              background: 'rgba(255,255,255,0.45)',
              border: '1px solid #FFF',
            },
          }"
          @itemClick="onPromptsItemClick"
        />
      </Space>

      <div class="sender-box">
        <!-- <Prompts
          :items="senderTopPromptsItems"
          @item-click="onPromptsItemClick"
          :style="{
            marginBottom: '5px',
            border: 'none',
            // display: 'flex',
            // justifyContent: 'flex-end',
          }"
        />-->
        <Sender
          ref="senderRef"
          submit-type="shiftEnter"
          :loading="!answerFinished"
          v-model:value="submitContent"
          @submit="onSubmit"
          :style="styles.sender"
          :allowSpeech="speechConfig"
          @cancel="onCancel"
        >
          <template #prefix>
            <Badge :dot="attachedShowFiles.length > 0 && !headerOpen">
              <Button type="text" :icon="h(PaperClipOutlined)" @click="handleFile" />
            </Badge>
          </template>
          <template #header>
            <Sender.Header
              title="上传文件"
              :open="headerOpen"
              :styles="{ content: { padding: 0 } }"
              @open-change="(open) => (headerOpen = open)"
            >
              <Attachments
                accept=".jpg,.png,.jpeg,.xls,.xlsx,.csv"
                name="files"
                :before-upload="() => true"
                v-model:items="attachedShowFiles"
                :action="uploadUrl"
                :headers="uploadHeader"
                @change="handleFileChange"
              >
                <template #placeholder="type">
                  <Flex
                    v-if="type && type.type === 'inline'"
                    align="center"
                    justify="center"
                    vertical
                    gap="2"
                  >
                    <Typography.Text style="font-size: 30px; line-height: 1">
                      <CloudUploadOutlined />
                    </Typography.Text>
                    <Typography.Title
                      :level="5"
                      style="margin: 0; font-size: 14px; line-height: 1.5"
                    >
                      上传文件
                    </Typography.Title>
                    <!-- <Typography.Text type="secondary"> 点击上传 </Typography.Text> -->
                  </Flex>
                  <Typography.Text v-if="type && type.type === 'drop'">
                    Drop file here
                  </Typography.Text>
                </template>
              </Attachments>
            </Sender.Header>
          </template>
        </Sender>
        <Prompts
          :items="senderPromptsItems"
          @item-click="onPromptsItemClick"
          :style="{ marginTop: '10px', border: 'none' }"
        />

        <div class="tooltip">内容由AI生成仅供参考</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ai-component {
  height: 100%;
  background: #f5f5f5;
}
</style>
